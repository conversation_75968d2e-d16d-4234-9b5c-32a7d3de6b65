import fs from 'fs';
import path from 'path';
import axios from 'axios';

/**
 * Enhanced IF Provider Test API
 * Following the approach from scripts/if-production-topup.js
 */

// Mask sensitive data helper (from the script)
const maskSensitive = (value, showChars = 4) => {
  if (!value) return '[NOT SET]';
  if (typeof value !== 'string') return '[OBJECT]';
  if (value.length <= showChars) return '****';
  return value.substring(0, showChars) + '****';
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { testType = 'full' } = req.body;
  const requestId = `test-${Date.now()}`;
  
  console.log(`[IF-TEST:${requestId}] Starting enhanced IF provider test`);

  try {
    // Load configuration (following the script's approach)
    const configPath = path.join(process.cwd(), 'components/taiwan/operators/if/config.json');
    
    if (!fs.existsSync(configPath)) {
      return res.status(404).json({
        success: false,
        error: 'Configuration file not found',
        path: configPath
      });
    }

    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    
    // Configuration verification (following the script)
    const configVerification = {
      merchantID: maskSensitive(config.merchantID),
      merchantPassword: config.merchantPassword ? 'SET' : 'NOT SET',
      apiKey: maskSensitive(config.apiKey),
      environment: process.env.NODE_ENV === 'production' ? 'PRODUCTION' : 'DEVELOPMENT',
      apiUrl: config.liveUrl || '[NOT SET]',
      testUrl: config.testUrl || '[NOT SET]',
      encryptionAlgorithm: config.encryption?.algorithm || '[NOT SET]',
      encryptionKeySet: config.encryption?.key ? 'YES' : 'NO',
      encryptionIVSet: config.encryption?.iv ? 'YES' : 'NO',
      productCount: config.productCodes?.length || 0
    };

    console.log(`[IF-TEST:${requestId}] Configuration verified:`, configVerification);

    const testResults = {
      requestId,
      timestamp: new Date().toISOString(),
      configVerification,
      tests: {}
    };

    // Test 1: Products API (following the script's validation approach)
    console.log(`[IF-TEST:${requestId}] Testing products API...`);
    try {
      const startTime = Date.now();
      const baseUrl = req.headers.host?.includes('localhost') 
        ? `http://${req.headers.host}` 
        : `https://${req.headers.host}`;
      
      const productsResponse = await axios.get(`${baseUrl}/api/payment/if/products`, {
        timeout: 10000,
        headers: {
          'X-Debug-Mode': 'true',
          'X-Request-ID': requestId
        }
      });

      const responseTime = Date.now() - startTime;
      
      testResults.tests.productsAPI = {
        status: 'success',
        responseTime,
        productCount: productsResponse.data.products?.length || 0,
        merchantID: productsResponse.data.config?.merchantID,
        apiUrl: productsResponse.data.config?.apiUrl,
        categorized: productsResponse.data.categorized
      };

      console.log(`[IF-TEST:${requestId}] Products API test passed (${responseTime}ms)`);
    } catch (error) {
      testResults.tests.productsAPI = {
        status: 'error',
        error: error.message,
        errorCode: error.response?.status,
        troubleshooting: ['Check if development server is running', 'Verify config file exists']
      };
      console.error(`[IF-TEST:${requestId}] Products API test failed:`, error.message);
    }

    // Test 2: Configuration validation (following the script's checks)
    console.log(`[IF-TEST:${requestId}] Validating configuration completeness...`);
    const configTests = {
      merchantCredentials: !!(config.merchantID && config.merchantPassword),
      apiEndpoints: !!(config.liveUrl && config.testUrl),
      encryption: !!(config.encryption?.key && config.encryption?.iv && config.encryption?.algorithm),
      products: !!(config.productCodes && config.productCodes.length > 0),
      apiKey: !!config.apiKey
    };

    const configScore = Object.values(configTests).filter(Boolean).length;
    const maxScore = Object.keys(configTests).length;

    testResults.tests.configuration = {
      status: configScore === maxScore ? 'success' : 'warning',
      score: `${configScore}/${maxScore}`,
      details: configTests,
      issues: Object.entries(configTests)
        .filter(([key, value]) => !value)
        .map(([key]) => `Missing or invalid: ${key}`)
    };

    // Test 3: Product categorization (following the script's grouping)
    if (config.productCodes) {
      const topupProducts = config.productCodes.filter(p => 
        p.name.toLowerCase().includes('recharge') || 
        p.name.toLowerCase().includes('instant')
      );
      const wirelessProducts = config.productCodes.filter(p => 
        p.name.toLowerCase().includes('wireless') || 
        p.name.toLowerCase().includes('plan')
      );

      const priceRange = {
        min: Math.min(...config.productCodes.map(p => p.price)),
        max: Math.max(...config.productCodes.map(p => p.price))
      };

      testResults.tests.products = {
        status: 'success',
        total: config.productCodes.length,
        categories: {
          topup: topupProducts.length,
          wireless: wirelessProducts.length
        },
        priceRange,
        sampleProducts: config.productCodes.slice(0, 3).map(p => ({
          id: p.id,
          name: p.name,
          price: p.price
        }))
      };
    }

    // Test 4: Environment validation (following the script's environment checks)
    testResults.tests.environment = {
      status: 'success',
      nodeEnv: process.env.NODE_ENV,
      nodeVersion: process.version,
      hostname: require('os').hostname(),
      timestamp: new Date().toISOString(),
      apiMode: process.env.NODE_ENV === 'production' ? 'Live API' : 'Test API'
    };

    // Overall test result
    const failedTests = Object.values(testResults.tests).filter(test => test.status === 'error').length;
    const warningTests = Object.values(testResults.tests).filter(test => test.status === 'warning').length;
    
    testResults.overall = {
      status: failedTests > 0 ? 'error' : warningTests > 0 ? 'warning' : 'success',
      summary: `${Object.keys(testResults.tests).length} tests completed`,
      failed: failedTests,
      warnings: warningTests,
      recommendations: []
    };

    // Add recommendations based on test results
    if (failedTests > 0) {
      testResults.overall.recommendations.push('Fix failed tests before using in production');
    }
    if (warningTests > 0) {
      testResults.overall.recommendations.push('Review configuration warnings');
    }
    if (testResults.tests.productsAPI?.status === 'success') {
      testResults.overall.recommendations.push('Products API is working correctly');
    }

    console.log(`[IF-TEST:${requestId}] Test completed:`, testResults.overall);

    return res.status(200).json({
      success: true,
      ...testResults
    });

  } catch (error) {
    console.error(`[IF-TEST:${requestId}] Test failed:`, error);
    return res.status(500).json({
      success: false,
      error: error.message,
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
