{"api": {"credentials": {"merchant_id": "MAGSHOP_TEST", "api_key": "YOUR_API_KEY", "api_secret": "Mw9p7QxR6aT2sZ8e", "api_endpoint": "https://api.familymart.com.tw/payment/gateway", "test_mode": true}}, "payment": {"limits": {"min_amount": 30, "max_amount": 30000, "expiry_hours": 24}, "fee": {"amount": 25, "currency": "NT$"}, "methods": [{"id": "FAMIPORT", "name": {"en": "FamiPort Payment", "vi": "Thanh toán qua FamiPort"}, "description": {"en": "Pay at FamilyMart FamiPort kiosk", "vi": "<PERSON><PERSON> to<PERSON> tại máy FamiPort của FamilyMart"}, "steps": {"en": ["Visit any FamilyMart convenience store in Taiwan", "Find the FamiPort kiosk machine (usually near the entrance)", "Select \"代碼繳費\" (Payment by Code) on the touchscreen", "Enter your payment code when prompted", "Verify the order amount", "Confirm the payment details and bring the printed receipt to the counter", "Pay the cashier the amount shown on the receipt", "Keep your receipt as proof of payment"], "vi": ["<PERSON><PERSON><PERSON> b<PERSON><PERSON> kỳ cử<PERSON> hàng FamilyMart nào tại <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (thường đặt gần cửa ra vào)", "Chọn \"代碼繳費\" (<PERSON><PERSON> <PERSON><PERSON> bằng mã) trên màn hình cảm <PERSON>ng", "<PERSON><PERSON><PERSON><PERSON> mã thanh to<PERSON> khi đư<PERSON><PERSON> yêu cầu", "<PERSON><PERSON><PERSON> tra số tiền đơn hàng", "<PERSON><PERSON><PERSON> nhận thông tin thanh toán và mang biên lai đến quầy thu ngân", "<PERSON><PERSON> toán số tiền hiển thị trên biên lai", "<PERSON><PERSON><PERSON> biên lai làm bằng chứng thanh toán"]}}, {"id": "BARCODE", "name": {"en": "Barcode Payment", "vi": "Thanh toán bằng mã vạch"}, "description": {"en": "Pay using barcode at FamilyMart counter", "vi": "<PERSON>h toán bằng mã vạch tại quầy FamilyMart"}, "steps": {"en": ["Visit any FamilyMart convenience store in Taiwan", "Show the barcode to the cashier", "Pay the amount shown on the screen", "Keep your receipt as proof of payment"], "vi": ["<PERSON><PERSON><PERSON> b<PERSON><PERSON> kỳ cử<PERSON> hàng FamilyMart nào tại <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> thị mã vạch cho nhân viên thu ngân", "<PERSON><PERSON> toán số tiền hiển thị trên màn hình", "<PERSON><PERSON><PERSON> biên lai làm bằng chứng thanh toán"]}}]}, "assets": {"logo": {"url": "https://cdn.jsdelivr.net/gh/abncharts/abncharts.public.1/abnasia.org/familymart-logo.webp", "width": 32, "height": 32}}, "api_specifications": {"version": "1.0", "endpoints": {"payment": {"path": "/payment/gateway", "method": "POST", "headers": {"Content-Type": "application/json"}}}, "request_fields": {"required": ["MerchantID", "MerchantTradeNo", "MerchantTradeDate", "TotalAmount", "TradeDesc", "ItemName", "ReturnURL", "ClientBackURL", "PaymentInfoURL", "PaymentType", "ChoosePayment", "StoreExpireDate", "CustomerName", "CustomerPhone", "CustomerEmail"], "optional": ["Desc_1", "Desc_2", "Desc_3", "Desc_4"]}, "response_fields": {"success": {"result_code": "0000", "payment_code": "string", "barcode": "string", "expire_date": "string"}, "error_codes": {"0001": {"en": "Invalid payment information", "vi": "Thông tin thanh to<PERSON> không hợp lệ"}, "0002": {"en": "Authentication error", "vi": "Lỗi xác thực"}, "0003": {"en": "Invalid amount or exceeds limit", "vi": "<PERSON><PERSON> tiền không hợp lệ hoặc vượt quá giới hạn"}}}}, "security": {"hash_algorithm": "sha256", "timeout": 30000}, "localization": {"currency": "NT$", "language": "vi", "date_format": {"display": "YYYY/MM/DD HH:mm", "api": "YYYYMMDDHHmm"}}, "phone_recharge": {"products": {"prepaid": ["IF150", "IF350", "IF599"], "extension": ["IF3M", "IF6M", "IF1Y"]}, "default_auto_recharge": true, "messages": {"title": {"en": "Phone Recharge Information", "vi": "Th<PERSON><PERSON> tin nạp tiền điện thoại"}, "product": {"en": "Product", "vi": "<PERSON><PERSON><PERSON> p<PERSON>m"}, "phone_number": {"en": "Phone Number", "vi": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "method": {"en": "Method", "vi": "<PERSON><PERSON><PERSON><PERSON> thức"}, "auto_recharge": {"en": "Auto recharge after payment", "vi": "Nạp tự động sau khi thanh toán"}, "manual_recharge": {"en": "Send recharge code after payment", "vi": "<PERSON><PERSON><PERSON> mã nạp sau khi thanh toán"}, "support_text": {"en": "Need recharge support?", "vi": "Cần hỗ trợ nạp tiền?"}, "contact_support": {"en": "Contact Customer Service", "vi": "<PERSON><PERSON><PERSON>"}}}, "form": {"title": {"en": "Personal Information", "vi": "Thông tin cá nhân"}, "fields": {"first_name": {"en": "First Name", "vi": "<PERSON><PERSON><PERSON>"}, "last_name": {"en": "Last Name", "vi": "Họ"}, "email": {"en": "Email", "vi": "Email"}, "phone": {"en": "Phone", "vi": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i"}, "address": {"en": "Address", "vi": "Địa chỉ"}, "message": {"en": "Message (Optional)", "vi": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>)"}}}, "order_summary": {"title": {"en": "Order Summary", "vi": "<PERSON><PERSON><PERSON> tắt đơn hàng"}, "order_id": {"en": "Order ID", "vi": "<PERSON><PERSON> đơn hàng"}, "subtotal": {"en": "Subtotal", "vi": "<PERSON><PERSON><PERSON> tiền hàng"}, "payment_fee": {"en": "Payment Fee", "vi": "<PERSON><PERSON>h to<PERSON>"}, "total": {"en": "Total", "vi": "<PERSON><PERSON>ng thanh toán"}}, "payment_instructions": {"title": {"en": "Payment Instructions", "vi": "Hướng dẫn thanh toán"}, "fee_notice": {"en": "Transaction Fee", "vi": "<PERSON><PERSON> giao d<PERSON>ch"}, "expiry_notice": {"en": "Please complete your payment within {hours} hours. After this time, the payment code will expire and you'll need to place a new order.", "vi": "<PERSON>ui lòng hoàn tất thanh toán trong vòng {hours} giờ. <PERSON>u thời gian này, mã thanh toán sẽ hết hạn và bạn cần đặt đơn hàng mới."}}, "thank_you": {"title": {"en": "Thank you for your order!", "vi": "Cảm ơn bạn đã đặt hàng!"}, "order_success": {"en": "Your order (#{orderId}) has been successfully submitted.", "vi": "<PERSON><PERSON><PERSON> hàng của bạn (#{orderId}) đã đ<PERSON><PERSON><PERSON> gửi thành công."}, "processing_notice": {"en": "After payment is completed, your order will be processed automatically. If you need support, please contact us through the following channels:", "vi": "<PERSON><PERSON> <PERSON>hi thanh to<PERSON> hoà<PERSON> tất, hệ thống sẽ tự động xử lý đơn hàng của bạn. N<PERSON><PERSON> cần hỗ trợ, vui lòng liên hệ với chúng tôi qua các kênh sau:"}, "view_order": {"en": "View your order", "vi": "<PERSON><PERSON> đ<PERSON>n hàng của bạn"}, "contact_support": {"en": "Contact support via Line", "vi": "Hỗ trợ qua Line"}}}